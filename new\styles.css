/* Root Variables */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  --card-shadow: 0 8px 25px rgba(0,0,0,0.1);
  --hover-shadow: 0 15px 35px rgba(0,0,0,0.15);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Body & Background */
body {
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.05)"/></svg>');
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Container */
.container {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  box-shadow: var(--card-shadow);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-top: 10px;
  padding: 20px;
  animation: slideUp 0.8s ease-out;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: #2c3e50;
  font-weight: 600;
}

h2 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 2rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

p, span, label {
  color: #555;
}

.text-muted {
  color: #6c757d !important;
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Sections */
.section {
  display: none;
  animation: fadeIn 0.5s ease-in-out;
}

.section.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  color: #333 !important;
  text-shadow: none;
  transition: var(--transition);
}

.navbar-brand:hover {
  transform: scale(1.05);
  color: #667eea !important;
}

.navbar-brand i {
  color: #667eea;
  margin-right: 8px;
}

.nav-link {
  transition: var(--transition);
  position: relative;
  color: #333 !important;
  font-weight: 500;
  margin: 0 5px;
}

.nav-link:hover {
  color: #667eea !important;
  transform: translateY(-2px);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: var(--transition);
  transform: translateX(-50%);
  border-radius: 2px;
}

.nav-link:hover::after {
  width: 80%;
}

.navbar-toggler {
  border: none;
  color: #333;
}

.navbar-toggler:focus {
  box-shadow: none;
}

/* Cards */
.card {
  border: none;
  box-shadow: var(--card-shadow);
  border-radius: 15px;
  transition: var(--transition);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transition: var(--transition);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--hover-shadow);
}

.card:hover::before {
  transform: scaleX(1);
}

.card-body {
  padding: 2rem;
}

/* Buttons */
.btn {
  border-radius: 12px;
  font-weight: 500;
  padding: 12px 24px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  border: none;
  color: rgb(0, 0, 0);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(123, 145, 255, 0.4);
}

.btn-success {
  background: var(--success-gradient);
  border: none;
  color: white;
}

.btn-warning {
  background: var(--warning-gradient);
  border: none;
  color: #333;
}

.btn-outline-primary {
  border: 2px solid #667eea;
  color: #667eea;
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--primary-gradient);
  border-color: transparent;
  color: white;
  transform: translateY(-2px);
}

/* Form Controls */
.form-control, .form-select {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 12px 16px;
  transition: var(--transition);
  background: rgba(248, 249, 250, 0.8);
}

.form-control:focus, .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  background: white;
  transform: translateY(-2px);
}

/* Progress & Badges */
.progress {
  height: 10px;
  border-radius: 10px;
  background: rgba(233, 236, 239, 0.3);
}

.progress-bar {
  background: var(--primary-gradient);
  border-radius: 10px;
  transition: width 0.6s ease;
}

.badge {
  font-size: 0.9rem;
  padding: 8px 12px;
  border-radius: 20px;
  font-weight: 500;
}

/* Quiz Specific */
#answers-grid button {
  width: 100%;
  text-align: left;
  padding: 1rem 1.5rem;
  margin-bottom: 0.8rem;
  border: 2px solid #e9ecef;
  background: white;
  border-radius: 12px;
  transition: var(--transition);
  position: relative;
}

#answers-grid button:hover {
  border-color: #667eea;
  transform: translateX(10px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
  color: black;
}

#answers-grid button.btn-success {
  background: #28a745 !important;
  border-color: #28a745 !important;
  color: black !important;
  animation: correctPulse 0.6s ease;
}

#answers-grid button.btn-success:hover {
  background: #218838 !important;
  border-color: #1e7e34 !important;
  color: black !important;
  transform: translateX(10px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4) !important;
}

#answers-grid button.btn-danger {
  background: #dc3545 !important;
  border-color: #dc3545 !important;
  color: black !important;
  animation: incorrectShake 0.6s ease;
}

#answers-grid button.btn-danger:hover {
  background: #c82333 !important;
  border-color: #bd2130 !important;
  color: black !important;
  transform: translateX(10px);
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4) !important;
}

@keyframes correctPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes incorrectShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Stats Cards Animation */
.card h3 {
  font-size: 2.5rem;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: countUp 1s ease-out;
}

@keyframes countUp {
  from { transform: scale(0.5); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Table Styles */
.table-responsive {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.table th {
  background: var(--primary-gradient);
  color: white;
  border: none;
  font-weight: 600;
  padding: 1rem;
}

.table td {
  padding: 1rem;
  border-color: rgba(0,0,0,0.05);
  transition: var(--transition);
}

.table tbody tr:hover {
  background: rgba(102, 126, 234, 0.05);
  transform: scale(1.01);
}

/* Chart Container */
canvas {
  max-height: 300px;
  border-radius: 10px;
}

/* Quiz Page Width Reduction */
.quiz-container {
  max-width: 66.67%; /* 2/3 of original width */
  margin: 0 auto;
}

/* Manage Page Width Reduction */
.manage-container {
  max-width: 66.67%; /* 2/3 of original width */
  margin: 0 auto;
}

/* Timer and Question Counter Background Color */
.badge.bg-secondary, .badge.bg-primary {
  background-color: #90EE90 !important; /* Light green */
  color: #333 !important; /* Dark text for better contrast */
}

/* Compact Layout Styles */
.compact-stats .card {
  margin-bottom: 0.5rem;
}

.compact-stats .card-body {
  padding: 0.75rem;
}

.compact-quiz .card-body {
  padding: 1rem;
}

.compact-quiz .progress {
  height: 6px;
  margin-bottom: 0.5rem;
}

.compact-quiz .badge {
  font-size: 0.8rem;
  padding: 0.4rem 0.8rem;
}

.compact-quiz #question-text {
  font-size: 1rem;
  line-height: 1.4;
}

.compact-quiz #answers-grid button {
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    margin: 10px;
    padding: 15px;
    border-radius: 15px;
  }

  #answers-grid button {
    padding: 0.6rem 0.8rem;
    margin-bottom: 0.4rem;
    font-size: 0.85rem;
  }

  .card-body {
    padding: 1rem;
  }

  .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
  }

  .card h3, .card h5, .card h6 {
    font-size: 1.2rem;
  }

  .compact-stats .card-body {
    padding: 0.5rem;
  }
}


